from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem,
    QLabel, QLineEdit, QPushButton, QHeaderView, QSpinBox, QMessageBox,
    QComboBox, QWidget, QListWidget, QListWidgetItem, QSplitter, QFrame,
    QGroupBox, QToolButton, QSizePolicy
)
from PyQt6.QtCore import Qt, pyqtSignal, QSettings, QTimer, QSize
from PyQt6.QtGui import QIcon, QFont, QColor
import asyncio
import json
from datetime import datetime

from app.core.services.inventory_service import InventoryService
from app.core.services.supplier_service import SupplierService
from app.utils.database import SessionLocal

class ProductSelectionDialog(QDialog):
    """Boîte de dialogue pour la sélection de produits"""

    # Signal émis lorsqu'un produit est sélectionné
    product_selected = pyqtSignal(dict)

    def __init__(self, parent=None):
        super().__init__(parent)

        # Services
        self.db = SessionLocal()
        self.service = InventoryService(self.db)
        self.supplier_service = SupplierService(self.db)

        # Données
        self.products = []
        self.filtered_products = []
        self.suppliers = []

        # Paramètres
        self.settings = QSettings("GestionMaintenance", "ProductSelection")

        # Configuration de l'interface
        self.setWindowTitle("Sélection de produit")
        self.setup_ui()

        # Charger les données
        self.load_data()

    def __del__(self):
        """Destructeur pour fermer la session"""
        if hasattr(self, 'db') and self.db:
            self.db.close()
            print("ProductSelectionDialog: Session de base de données fermée")

    def setup_ui(self):
        """Configure l'interface utilisateur"""
        self.resize(1200, 700)

        main_layout = QVBoxLayout(self)

        # === NOUVELLE STRUCTURE : TABLEAU SUR TOUTE LA LARGEUR ===

        # Section supérieure : Scan de code-barres et recherche sur une ligne
        top_section = QHBoxLayout()

        # Champ de scan de code-barres (sans header) - élargi
        self.barcode_edit = QLineEdit()
        self.barcode_edit.setPlaceholderText("Scanner ou saisir un code-barres...")
        self.barcode_edit.returnPressed.connect(self.scan_barcode)
        self.barcode_edit.setMinimumWidth(200)
        top_section.addWidget(self.barcode_edit, 1)  # Facteur d'étirement de 1

        # Bouton de scan
        scan_button = QPushButton("Rechercher")
        scan_button.setIcon(QIcon("app/ui/resources/icons/barcode.svg"))
        scan_button.clicked.connect(self.scan_barcode)
        top_section.addWidget(scan_button)

        # Champ de recherche principal élargi (sans label "Rechercher")
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("Nom, SKU, description...")
        self.search_edit.textChanged.connect(self.filter_products)
        # Élargir la barre de recherche en lui donnant plus d'espace
        self.search_edit.setMinimumWidth(300)
        top_section.addWidget(self.search_edit, 2)  # Facteur d'étirement de 2

        # Filtre par catégorie
        self.category_combo = QComboBox()
        self.category_combo.addItem("Toutes", None)
        self.category_combo.currentIndexChanged.connect(self.filter_products)
        top_section.addWidget(QLabel("Catégorie:"))
        top_section.addWidget(self.category_combo)

        # Filtre par fournisseur
        self.supplier_combo = QComboBox()
        self.supplier_combo.addItem("Tous", None)
        self.supplier_combo.currentIndexChanged.connect(self.filter_products)
        top_section.addWidget(QLabel("Fournisseur:"))
        top_section.addWidget(self.supplier_combo)

        # Ajouter la section supérieure au layout principal
        main_layout.addLayout(top_section)

        # === TABLEAU DES PRODUITS SUR TOUTE LA LARGEUR ===
        self.products_table = QTableWidget()
        self.products_table.setColumnCount(7)
        self.products_table.setHorizontalHeaderLabels([
            "SKU", "Nom", "Prix", "Stock", "Catégorie", "Fournisseur", "Emplacement"
        ])

        # Configuration du tableau pour utiliser toute la largeur
        header = self.products_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)  # SKU
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)           # Nom (extensible)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)  # Prix
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)  # Stock
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)  # Catégorie
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)  # Fournisseur
        header.setSectionResizeMode(6, QHeaderView.ResizeMode.ResizeToContents)  # Emplacement

        self.products_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.products_table.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)
        self.products_table.itemDoubleClicked.connect(self.on_item_double_clicked)
        self.products_table.itemSelectionChanged.connect(self.on_selection_changed)

        # Style pour mettre en évidence la sélection
        self.products_table.setStyleSheet("""
            QTableWidget {
                selection-background-color: #2a82da;
                selection-color: white;
                alternate-background-color: #f5f5f5;
                gridline-color: #d0d0d0;
                font-size: 12px;
            }
            QTableWidget::item:selected {
                background-color: #2a82da;
                color: white;
                font-weight: bold;
            }
            QTableWidget::item:hover {
                background-color: #e0e0e0;
            }
            QHeaderView::section {
                background-color: #f0f0f0;
                padding: 8px;
                border: 1px solid #d0d0d0;
                font-weight: bold;
            }
        """)

        # Activer les lignes alternées pour une meilleure lisibilité
        self.products_table.setAlternatingRowColors(True)

        # Ajouter le tableau au layout principal (prend toute la largeur)
        main_layout.addWidget(self.products_table)

        # === SECTION DE SÉLECTION EN BAS ===
        selection_group = QGroupBox("Sélection du produit")
        selection_layout = QHBoxLayout(selection_group)

        # Informations du produit sélectionné
        self.selected_info = QLabel("Aucun produit sélectionné")
        self.selected_info.setStyleSheet("font-weight: bold; color: #2a82da;")
        selection_layout.addWidget(self.selected_info)

        # Espacement flexible
        selection_layout.addStretch()

        # Quantité
        quantity_label = QLabel("Quantité:")
        selection_layout.addWidget(quantity_label)

        self.quantity_spin = QSpinBox()
        self.quantity_spin.setRange(1, 1000)
        self.quantity_spin.setValue(1)
        self.quantity_spin.setMinimumWidth(80)
        selection_layout.addWidget(self.quantity_spin)

        # Boutons
        self.select_button = QPushButton("✅ Sélectionner")
        self.select_button.setIcon(QIcon("app/ui/resources/icons/check.svg"))
        self.select_button.clicked.connect(self.select_product)
        self.select_button.setEnabled(False)
        self.select_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
            }
        """)
        selection_layout.addWidget(self.select_button)

        self.cancel_button = QPushButton("❌ Annuler")
        self.cancel_button.setIcon(QIcon("app/ui/resources/icons/cancel.svg"))
        self.cancel_button.clicked.connect(self.reject)
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        selection_layout.addWidget(self.cancel_button)

        # Ajouter la section de sélection au layout principal
        main_layout.addWidget(selection_group)

    def load_data(self):
        """Charge les données des produits et des fournisseurs"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            # Récupérer tous les produits
            products = loop.run_until_complete(self.service.get_all())

            # Récupérer tous les fournisseurs
            suppliers = loop.run_until_complete(self.supplier_service.get_all())

            # Stocker les produits et les fournisseurs
            self.products = products
            self.filtered_products = products
            self.suppliers = suppliers

            # Remplir le tableau
            self._refresh_products_table()

            # Remplir les catégories
            categories = set()
            for product in products:
                categories.add(product.category.value)

            # Ajouter les catégories au combo
            for category in sorted(categories):
                self.category_combo.addItem(category.capitalize(), category)

            # Remplir les fournisseurs
            for supplier in suppliers:
                if supplier.active:  # Ne pas afficher les fournisseurs inactifs
                    self.supplier_combo.addItem(supplier.name, supplier.id)

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement des données: {str(e)}")
            print(f"Erreur lors du chargement des données: {e}")
            import traceback
            traceback.print_exc()
        finally:
            loop.close()

    def _refresh_products_table(self):
        """Rafraîchit le tableau des produits"""
        self.products_table.setRowCount(0)

        for i, product in enumerate(self.filtered_products):
            self.products_table.insertRow(i)

            # SKU
            self.products_table.setItem(i, 0, QTableWidgetItem(product.sku))

            # Nom
            self.products_table.setItem(i, 1, QTableWidgetItem(product.name))

            # Prix
            price_item = QTableWidgetItem(f"{product.unit_price:.2f} DA")
            price_item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
            self.products_table.setItem(i, 2, price_item)

            # Stock
            stock_item = QTableWidgetItem(str(product.quantity))
            stock_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)

            # Colorer en fonction du stock
            if product.quantity <= 0:
                stock_item.setForeground(Qt.GlobalColor.red)
            elif product.quantity <= product.minimum_quantity:
                stock_item.setForeground(Qt.GlobalColor.darkYellow)

            self.products_table.setItem(i, 3, stock_item)

            # Catégorie
            self.products_table.setItem(i, 4, QTableWidgetItem(product.category.value.capitalize()))

            # Fournisseur
            supplier_name = "Non spécifié"
            if product.supplier_id:
                for supplier in self.suppliers:
                    if supplier.id == product.supplier_id:
                        supplier_name = supplier.name
                        break
            self.products_table.setItem(i, 5, QTableWidgetItem(supplier_name))

            # Emplacement
            self.products_table.setItem(i, 6, QTableWidgetItem(product.location))

    def filter_products(self):
        """Filtre les produits selon les critères de recherche"""
        search_text = self.search_edit.text().lower()
        category = self.category_combo.currentData()
        supplier_id = self.supplier_combo.currentData()

        # Filtrer les produits
        self.filtered_products = []
        for product in self.products:
            # Filtrer par texte
            if search_text:
                if (search_text not in product.name.lower() and
                    search_text not in product.sku.lower() and
                    search_text not in product.description.lower()):
                    continue

            # Filtrer par catégorie
            if category and product.category.value != category:
                continue

            # Filtrer par fournisseur
            if supplier_id and product.supplier_id != supplier_id:
                continue

            # Ajouter le produit filtré
            self.filtered_products.append(product)

        # Rafraîchir le tableau
        self._refresh_products_table()

    def on_selection_changed(self):
        """Gère le changement de sélection dans le tableau"""
        has_selection = len(self.products_table.selectedItems()) > 0
        self.select_button.setEnabled(has_selection)

        if has_selection:
            # Récupérer le produit sélectionné
            row = self.products_table.currentRow()
            product = self.filtered_products[row]

            # Afficher les informations du produit sélectionné
            info_text = f"📦 {product.name} | 💰 {product.unit_price:.2f} DA | 📊 Stock: {product.quantity}"
            if hasattr(product, 'category') and product.category:
                info_text += f" | 🏷️ {product.category.name}"
            self.selected_info.setText(info_text)

            # Limiter la quantité au stock disponible
            self.quantity_spin.setMaximum(max(1, product.quantity))
        else:
            self.selected_info.setText("Aucun produit sélectionné")

    def on_item_double_clicked(self, item):
        """Gère le double-clic sur un élément du tableau"""
        self.select_product()





    def scan_barcode(self):
        """Recherche un produit par code-barres"""
        barcode = self.barcode_edit.text().strip()
        if not barcode:
            return

        # Rechercher le produit par code-barres (SKU)
        found = False
        for i, product in enumerate(self.products):
            if product.sku.lower() == barcode.lower():
                # Réinitialiser les filtres
                self.search_edit.clear()
                self.category_combo.setCurrentIndex(0)
                self.supplier_combo.setCurrentIndex(0)
                self.filter_products()

                # Rechercher le produit dans le tableau
                for j, filtered_product in enumerate(self.filtered_products):
                    if filtered_product.id == product.id:
                        # Sélectionner la ligne dans le tableau
                        self.products_table.selectRow(j)
                        # Mettre à jour la prévisualisation
                        self.update_preview()
                        found = True
                        break

                if found:
                    break

        if not found:
            QMessageBox.warning(
                self,
                "Produit non trouvé",
                f"Aucun produit trouvé avec le code-barres {barcode}"
            )

        # Effacer le champ de code-barres
        self.barcode_edit.clear()



    def select_product(self):
        """Sélectionne le produit et ferme la boîte de dialogue"""
        if not self.products_table.selectedItems():
            return

        # Récupérer le produit sélectionné
        row = self.products_table.currentRow()
        product = self.filtered_products[row]

        # Récupérer la quantité
        quantity = self.quantity_spin.value()

        # Vérifier le stock
        if quantity > product.quantity:
            QMessageBox.warning(
                self,
                "Stock insuffisant",
                f"Stock insuffisant pour {product.name}. Disponible: {product.quantity}"
            )
            return

        # Créer les données du produit
        product_data = {
            'product_id': product.id,
            'name': product.name,
            'unit_price': product.unit_price,
            'quantity': quantity,
            'discount_percent': 0,
            'tax_percent': 0,
            'total': product.unit_price * quantity
        }

        # Émettre le signal
        self.product_selected.emit(product_data)

        # Fermer la boîte de dialogue
        self.accept()

"""
Widget pour afficher les détails de facturation d'une commande d'achat
"""
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QFrame, QGroupBox, QTableWidget,
    QTableWidgetItem, QHeaderView
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QIcon, QColor
from datetime import datetime
import asyncio

from app.core.services.purchasing_service import PurchasingService
from app.utils.database import SessionLocal
from app.ui.components.custom_widgets import LoadingOverlay


class InvoiceDetailsWidget(QWidget):
    """Widget affichant les détails de facturation d'une commande d'achat"""

    # Signaux
    invoiceCreated = pyqtSignal(int)   # ID de la commande pour laquelle une facture a été créée

    def __init__(self, parent=None):
        super().__init__(parent)
        self.order = None
        self.order_id = None
        self.financial_data = None

        # Services
        self.db = SessionLocal()
        self.purchasing_service = PurchasingService(self.db)

        # Overlay de chargement
        self.loading_overlay = LoadingOverlay(self)

        self.setup_ui()

    def setup_ui(self):
        """Configure l'interface utilisateur"""
        main_layout = QVBoxLayout(self)

        # Titre
        self.title_label = QLabel("Détails de facturation")
        self.title_label.setObjectName("sectionSubHeader")
        main_layout.addWidget(self.title_label)

        # Sections encadrées, chaque item en horizontal (label à gauche, valeur à droite)
        frame_style = "QFrame { border: none; background: transparent; }"
        title_style = "color: #555; font-weight: 600; font-size: 12px;"
        value_style = "color: #222; font-size: 14px; font-weight: 700;"

        def make_item(title, value_label_attr, word_wrap=False):
            w = QWidget()
            h = QHBoxLayout(w)
            h.setContentsMargins(6, 6, 6, 6)
            h.setSpacing(6)
            t = QLabel(title)
            t.setStyleSheet(title_style)
            t.setFixedWidth(140)
            t.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)
            h.addWidget(t)
            val = QLabel("-")
            val.setStyleSheet(value_style)
            val.setWordWrap(word_wrap)
            val.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)
            h.addWidget(val, 1)
            setattr(self, value_label_attr, val)
            return w

        # Frame 1 - Montants (colonne gauche)
        frame1 = QFrame()
        frame1.setStyleSheet(frame_style)
        f1 = QVBoxLayout(frame1)
        f1.setContentsMargins(8, 8, 8, 8)
        f1.setSpacing(8)
        f1.addWidget(make_item("Total facturé", "total_invoiced_label"))
        f1.addWidget(make_item("Montant restant", "remaining_to_invoice_label"))

        # Frame 2 - Statut et fournisseur (colonne centrale)
        frame2 = QFrame()
        frame2.setStyleSheet(frame_style)
        f2 = QVBoxLayout(frame2)
        f2.setContentsMargins(8, 8, 8, 8)
        f2.setSpacing(8)
        f2.addWidget(make_item("Statut facturation", "invoice_status_label"))
        f2.addWidget(make_item("Fournisseur", "supplier_name_label"))

        # Frame 3 - Conditions de paiement (colonne droite)
        frame3 = QFrame()
        frame3.setStyleSheet(frame_style)
        f3 = QVBoxLayout(frame3)
        f3.setContentsMargins(8, 8, 8, 8)
        f3.setSpacing(8)
        f3.addWidget(make_item("Conditions paiement", "payment_terms_label"))
        f3.addWidget(make_item("Date d'échéance", "payment_due_date_label"))

        # Disposer les 3 frames côte à côte
        row_layout = QHBoxLayout()
        row_layout.setSpacing(12)
        row_layout.addWidget(frame1, 1)
        row_layout.addWidget(frame2, 1)
        row_layout.addWidget(frame3, 1)
        main_layout.addLayout(row_layout)

        # Espacement
        main_layout.addSpacing(10)

        # Titre du tableau des factures
        invoices_title = QLabel("Factures")
        invoices_title.setObjectName("sectionSubHeader")
        invoices_title.setStyleSheet("font-weight: bold; font-size: 14px; color: #333; margin-bottom: 5px;")
        main_layout.addWidget(invoices_title)

        # Tableau des factures
        self.invoices_table = QTableWidget(0, 7)
        self.invoices_table.setHorizontalHeaderLabels([
            "N° Facture", "Date", "Date d'échéance", "Montant", "Montant TVA", "Statut", "Notes"
        ])

        # Configuration des colonnes avec largeurs optimisées
        header = self.invoices_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)  # N° Facture
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)  # Date
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)  # Date d'échéance
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)  # Montant
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)  # Montant TVA
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)  # Statut
        header.setSectionResizeMode(6, QHeaderView.ResizeMode.Stretch)           # Notes

        self.invoices_table.setAlternatingRowColors(True)
        self.invoices_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.invoices_table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)
        self.invoices_table.setMinimumHeight(200)  # Hauteur minimum pour le tableau
        main_layout.addWidget(self.invoices_table)

        # Espacement
        main_layout.addSpacing(10)

        # Titre des actions
        actions_title = QLabel("Actions")
        actions_title.setObjectName("sectionSubHeader")
        actions_title.setStyleSheet("font-weight: bold; font-size: 14px; color: #333; margin-bottom: 5px;")
        main_layout.addWidget(actions_title)

        # Actions de facturation
        actions_layout = QHBoxLayout()
        actions_layout.setSpacing(10)
        actions_layout.setContentsMargins(0, 0, 0, 0)

        self.create_invoice_button = QPushButton("Créer une facture")
        self.create_invoice_button.setIcon(QIcon("app/ui/resources/icons/invoice.svg"))
        self.create_invoice_button.setObjectName("successButton")
        self.create_invoice_button.clicked.connect(self._on_create_invoice_clicked)
        actions_layout.addWidget(self.create_invoice_button)

        self.view_invoice_button = QPushButton("Voir la facture")
        self.view_invoice_button.setIcon(QIcon("app/ui/resources/icons/view.svg"))
        self.view_invoice_button.setObjectName("infoButton")
        self.view_invoice_button.clicked.connect(self._on_view_invoice_clicked)
        self.view_invoice_button.setEnabled(False)
        actions_layout.addWidget(self.view_invoice_button)

        self.print_invoice_button = QPushButton("Imprimer facture")
        self.print_invoice_button.setIcon(QIcon("app/ui/resources/icons/print.svg"))
        self.print_invoice_button.setObjectName("primaryButton")
        self.print_invoice_button.clicked.connect(self._on_print_invoice_clicked)
        self.print_invoice_button.setEnabled(False)
        actions_layout.addWidget(self.print_invoice_button)

        # Ajouter un stretch pour pousser les boutons vers la gauche
        actions_layout.addStretch()

        main_layout.addLayout(actions_layout)

        # Espacement
        main_layout.addStretch()

    def set_order(self, order):
        """Définit la commande à afficher"""
        self.order = order
        self.order_id = order.id if order else None

        if not order:
            self._clear_details()
            return

        # Mettre à jour les informations de base
        self._update_basic_info()

        # Charger les données financières de manière asynchrone
        QTimer.singleShot(0, self._load_financial_data_wrapper)

    def _update_basic_info(self):
        """Met à jour les informations de base"""
        if not self.order:
            return

        # Informations fournisseur
        supplier = getattr(self.order, 'supplier', None)
        supplier_name = getattr(supplier, 'name', "N/A") if supplier else "N/A"
        self.supplier_name_label.setText(supplier_name)

        # Conditions de paiement
        payment_terms = getattr(self.order, 'payment_terms', None)
        self.payment_terms_label.setText(payment_terms or "N/A")

        # Date d'échéance
        payment_due_date = getattr(self.order, 'payment_due_date', None)
        if payment_due_date:
            self.payment_due_date_label.setText(payment_due_date.strftime("%d/%m/%Y"))
        else:
            self.payment_due_date_label.setText("N/A")

    def _clear_details(self):
        """Efface les détails affichés"""
        self.total_invoiced_label.setText("0.00 DA")
        self.remaining_to_invoice_label.setText("0.00 DA")
        self.invoice_status_label.setText("Non facturé")
        self.supplier_name_label.setText("N/A")
        self.payment_terms_label.setText("N/A")
        self.payment_due_date_label.setText("N/A")

        # Vider le tableau
        self.invoices_table.setRowCount(0)

        # Désactiver les boutons
        self.create_invoice_button.setEnabled(False)
        self.view_invoice_button.setEnabled(False)
        self.print_invoice_button.setEnabled(False)

    def _load_financial_data_wrapper(self):
        """Wrapper pour charger les données financières"""
        if self.order_id:
            self.loading_overlay.show()
            # Utiliser QTimer pour exécuter de manière asynchrone
            QTimer.singleShot(0, self._load_financial_data_sync)

    def _load_financial_data_sync(self):
        """Charge les données financières de manière synchrone"""
        try:
            # Utiliser la version synchrone du service si disponible
            if hasattr(self.purchasing_service, 'update_payment_status_sync'):
                payment_status = self.purchasing_service.update_payment_status_sync(self.order_id)
            else:
                # Fallback : créer une nouvelle boucle d'événements
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    payment_status = loop.run_until_complete(
                        self.purchasing_service.update_payment_status(self.order_id)
                    )
                finally:
                    loop.close()
            
            self.financial_data = payment_status

            # Mettre à jour l'interface
            self._update_invoice_status(payment_status)
            self._update_invoices_table(payment_status.get("invoices", []))

            # Mettre à jour les boutons d'action
            self._update_action_buttons()

        except Exception as e:
            print(f"Erreur lors du chargement des données de facturation: {str(e)}")
            import traceback
            traceback.print_exc()
        finally:
            self.loading_overlay.hide()

    def _update_invoice_status(self, payment_status):
        """Met à jour les informations de statut de facturation"""
        if not payment_status:
            return

        # Montants
        total_invoiced = payment_status.get('total_invoiced', 0)
        total_amount = self.order.total_amount if self.order and self.order.total_amount else 0
        remaining_to_invoice = max(0, total_amount - total_invoiced)

        self.total_invoiced_label.setText(f"{total_invoiced:.2f} DA")
        self.remaining_to_invoice_label.setText(f"{remaining_to_invoice:.2f} DA")

        # Statut de facturation
        if total_invoiced == 0:
            status = "Non facturé"
        elif remaining_to_invoice == 0:
            status = "Entièrement facturé"
        else:
            status = "Partiellement facturé"
        
        self.invoice_status_label.setText(status)

    def _update_invoices_table(self, invoices):
        """Met à jour le tableau des factures"""
        self.invoices_table.setRowCount(len(invoices))

        for row, invoice in enumerate(invoices):
            # N° Facture
            self.invoices_table.setItem(row, 0, QTableWidgetItem(str(invoice.get("number", ""))))

            # Date de facture
            date_str = ""
            if invoice.get("date"):
                if isinstance(invoice["date"], str):
                    date_str = invoice["date"]
                else:
                    date_str = invoice["date"].strftime("%d/%m/%Y")
            self.invoices_table.setItem(row, 1, QTableWidgetItem(date_str))

            # Date d'échéance
            due_date_str = ""
            if invoice.get("due_date"):
                if isinstance(invoice["due_date"], str):
                    due_date_str = invoice["due_date"]
                else:
                    due_date_str = invoice["due_date"].strftime("%d/%m/%Y")
            self.invoices_table.setItem(row, 2, QTableWidgetItem(due_date_str))

            # Montant
            self.invoices_table.setItem(row, 3, QTableWidgetItem(f"{invoice.get('amount', 0):.2f} DA"))

            # Montant TVA
            tax_amount = invoice.get('tax_amount', 0)
            self.invoices_table.setItem(row, 4, QTableWidgetItem(f"{tax_amount:.2f} DA"))

            # Statut avec couleur
            status_item = QTableWidgetItem(self._get_invoice_status_display(invoice.get("status", "")))
            if invoice.get("status") == "paid":
                status_item.setForeground(QColor("green"))
            elif invoice.get("status") == "partial":
                status_item.setForeground(QColor("orange"))
            elif invoice.get("status") == "overdue":
                status_item.setForeground(QColor("red"))
            self.invoices_table.setItem(row, 5, status_item)

            # Notes
            notes = invoice.get("notes", "")
            notes_item = QTableWidgetItem(notes)
            notes_item.setToolTip(notes)  # Afficher les notes complètes au survol
            self.invoices_table.setItem(row, 6, notes_item)

        # Activer/désactiver les boutons selon la sélection
        self.view_invoice_button.setEnabled(len(invoices) > 0)
        self.print_invoice_button.setEnabled(len(invoices) > 0)

    def _update_action_buttons(self):
        """Met à jour l'état des boutons d'action"""
        if not self.order:
            return

        # Activer les boutons selon le statut de la commande
        valid_statuses = ["partially_received", "completed"]
        order_status = self.order.status.value if hasattr(self.order.status, 'value') else str(self.order.status)

        # Créer une facture : pour les commandes partiellement reçues ou terminées
        self.create_invoice_button.setEnabled(order_status.lower() in valid_statuses)

    def _get_invoice_status_display(self, status):
        """Retourne l'affichage du statut de facture"""
        status_display = {
            "pending": "En attente",
            "partial": "Partiellement payé",
            "paid": "Payé",
            "overdue": "En retard",
            "cancelled": "Annulé"
        }
        return status_display.get(status.lower(), str(status))

    def _on_create_invoice_clicked(self):
        """Gère le clic sur le bouton de création de facture"""
        if self.order:
            self.invoiceCreated.emit(self.order.id)

    def _on_view_invoice_clicked(self):
        """Gère le clic sur le bouton de visualisation de facture"""
        # TODO: Implémenter la visualisation de facture
        pass

    def _on_print_invoice_clicked(self):
        """Gère le clic sur le bouton d'impression de facture"""
        # TODO: Implémenter l'impression de facture
        pass

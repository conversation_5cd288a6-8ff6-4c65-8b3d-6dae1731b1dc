"""
Dialogue pour la gestion des clôtures journalières.
"""
from PyQt6.QtWidgets import (
    Q<PERSON>ialog, QVBoxLayout, QHBoxLayout, QTabWidget, QWidget,
    QLabel, QDateEdit, QPushButton, QTableWidget, QTableWidgetItem,
    QHeaderView, QFormLayout, QGroupBox, QTextEdit, QProgressBar,
    QMessageBox, QSplitter, QFrame, QCheckBox, QSpinBox
)
from PyQt6.QtCore import Qt, QDate, QThread, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QColor, QPalette
from datetime import datetime, date, timedelta
from decimal import Decimal
from typing import List, Dict, Any, Optional
import asyncio

from app.core.services.daily_closure_service import DailyClosureService, ClosureValidationError
from app.core.models.daily_closure import DailyClosure, ClosureStatus, CashRegisterSnapshot
from app.utils.database import SessionLocal
from app.utils.event_bus import event_bus


class ClosureExecutionThread(QThread):
    """Thread pour l'exécution des clôtures en arrière-plan"""
    
    progress_updated = pyqtSignal(int, str)  # pourcentage, message
    closure_completed = pyqtSignal(object)  # DailyClosure
    error_occurred = pyqtSignal(str)
    
    def __init__(self, closure_service: DailyClosureService, closure_date: date, user_id: int):
        super().__init__()
        self.closure_service = closure_service
        self.closure_date = closure_date
        self.user_id = user_id
    
    def run(self):
        """Exécute la clôture"""
        try:
            self.progress_updated.emit(10, "Démarrage de la clôture...")
            
            # Démarrer la clôture
            closure = self.closure_service.start_daily_closure(
                self.closure_date, self.user_id
            )
            
            self.progress_updated.emit(30, "Création des snapshots...")
            
            # Créer les snapshots
            snapshots = self.closure_service.create_snapshots(closure)
            
            self.progress_updated.emit(60, "Validation des données...")
            
            # Effectuer les validations
            validations = self.closure_service.validate_closure(closure)
            
            self.progress_updated.emit(80, "Finalisation de la clôture...")
            
            # Terminer la clôture
            completed_closure = self.closure_service.complete_daily_closure(
                closure.id, self.user_id
            )
            
            self.progress_updated.emit(100, "Clôture terminée avec succès")
            self.closure_completed.emit(completed_closure)
            
        except Exception as e:
            self.error_occurred.emit(str(e))


class DailyClosureDialog(QDialog):
    """Dialogue pour la gestion des clôtures journalières"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.setWindowTitle("Clôtures Journalières")
        self.setModal(True)
        self.resize(1200, 800)
        
        # Services
        self.db = SessionLocal()
        self.closure_service = DailyClosureService(self.db)
        
        # État
        self.current_user_id = 1  # TODO: Récupérer l'utilisateur connecté
        self.execution_thread = None
        
        # Interface
        self.setup_ui()
        self.load_data()
    
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        layout = QVBoxLayout(self)
        
        # Onglets principaux
        self.tabs = QTabWidget()
        layout.addWidget(self.tabs)
        
        # Onglet nouvelle clôture
        self.new_closure_tab = self.create_new_closure_tab()
        self.tabs.addTab(self.new_closure_tab, "Nouvelle Clôture")
        
        # Onglet historique
        self.history_tab = self.create_history_tab()
        self.tabs.addTab(self.history_tab, "Historique")
        
        # Onglet verrouillages
        self.locks_tab = self.create_locks_tab()
        self.tabs.addTab(self.locks_tab, "Verrouillages")
        
        # Boutons
        buttons_layout = QHBoxLayout()
        
        self.refresh_button = QPushButton("Actualiser")
        self.refresh_button.clicked.connect(self.load_data)
        buttons_layout.addWidget(self.refresh_button)
        
        buttons_layout.addStretch()
        
        close_button = QPushButton("Fermer")
        close_button.clicked.connect(self.close)
        buttons_layout.addWidget(close_button)
        
        layout.addLayout(buttons_layout)
    
    def create_new_closure_tab(self) -> QWidget:
        """Crée l'onglet de nouvelle clôture"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Section de sélection de date
        date_group = QGroupBox("Sélection de la date")
        date_group.setMinimumHeight(120)  # Hauteur minimale pour éviter le masquage
        date_layout = QFormLayout(date_group)
        
        self.closure_date_edit = QDateEdit()
        self.closure_date_edit.setDate(QDate.currentDate().addDays(-1))  # Hier par défaut
        self.closure_date_edit.setCalendarPopup(True)
        self.closure_date_edit.dateChanged.connect(self.on_date_changed)
        date_layout.addRow("Date de clôture:", self.closure_date_edit)
        
        # Statut de la date
        self.date_status_label = QLabel()
        date_layout.addRow("Statut:", self.date_status_label)
        
        layout.addWidget(date_group)
        
        # Section de prévisualisation
        preview_group = QGroupBox("Prévisualisation")
        preview_layout = QVBoxLayout(preview_group)
        
        # Tableau des caisses
        self.preview_table = QTableWidget()
        self.preview_table.setColumnCount(6)
        self.preview_table.setHorizontalHeaderLabels([
            "Caisse", "Solde Ouverture", "Solde Clôture", "Entrées", "Sorties", "Écart"
        ])
        self.preview_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        preview_layout.addWidget(self.preview_table)
        
        # Résumé
        self.summary_label = QLabel()
        self.summary_label.setStyleSheet("font-weight: bold; padding: 10px; background-color: #f0f0f0;")
        preview_layout.addWidget(self.summary_label)
        
        layout.addWidget(preview_group)
        
        # Barre de progression
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        self.progress_label = QLabel()
        self.progress_label.setVisible(False)
        layout.addWidget(self.progress_label)
        
        # Boutons d'action
        action_layout = QHBoxLayout()
        
        self.preview_button = QPushButton("Prévisualiser")
        self.preview_button.clicked.connect(self.preview_closure)
        action_layout.addWidget(self.preview_button)
        
        self.execute_button = QPushButton("Exécuter la Clôture")
        self.execute_button.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }")
        self.execute_button.clicked.connect(self.execute_closure)
        self.execute_button.setEnabled(False)
        action_layout.addWidget(self.execute_button)
        
        action_layout.addStretch()
        
        layout.addLayout(action_layout)
        
        return widget
    
    def create_history_tab(self) -> QWidget:
        """Crée l'onglet d'historique"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Tableau d'historique
        self.history_table = QTableWidget()
        self.history_table.setColumnCount(7)
        self.history_table.setHorizontalHeaderLabels([
            "Date", "Statut", "Caisses", "Solde Total", "Transactions", "Démarré par", "Terminé le"
        ])
        self.history_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.history_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.history_table.itemDoubleClicked.connect(self.view_closure_details)
        layout.addWidget(self.history_table)
        
        # Boutons d'action pour l'historique
        history_buttons = QHBoxLayout()
        
        self.view_details_button = QPushButton("Voir Détails")
        self.view_details_button.clicked.connect(self.view_closure_details)
        self.view_details_button.setEnabled(False)
        history_buttons.addWidget(self.view_details_button)
        
        self.cancel_closure_button = QPushButton("Annuler Clôture")
        self.cancel_closure_button.clicked.connect(self.cancel_closure)
        self.cancel_closure_button.setEnabled(False)
        history_buttons.addWidget(self.cancel_closure_button)
        
        history_buttons.addStretch()
        
        layout.addLayout(history_buttons)
        
        # Connecter la sélection
        self.history_table.itemSelectionChanged.connect(self.on_history_selection_changed)
        
        return widget
    
    def create_locks_tab(self) -> QWidget:
        """Crée l'onglet des verrouillages"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Tableau des verrouillages
        self.locks_table = QTableWidget()
        self.locks_table.setColumnCount(6)
        self.locks_table.setHorizontalHeaderLabels([
            "Début", "Fin", "Type", "Raison", "Verrouillé par", "Date"
        ])
        self.locks_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        layout.addWidget(self.locks_table)
        
        return widget
    
    def on_date_changed(self):
        """Gère le changement de date"""
        selected_date = self.closure_date_edit.date().toPyDate()
        
        # Vérifier si la date peut être clôturée
        can_close, reason = self.closure_service.can_close_date(selected_date)
        
        if can_close:
            self.date_status_label.setText("✓ Peut être clôturée")
            self.date_status_label.setStyleSheet("color: green; font-weight: bold;")
            self.preview_button.setEnabled(True)
        else:
            self.date_status_label.setText(f"✗ {reason}")
            self.date_status_label.setStyleSheet("color: red; font-weight: bold;")
            self.preview_button.setEnabled(False)
            self.execute_button.setEnabled(False)
        
        # Effacer la prévisualisation
        self.preview_table.setRowCount(0)
        self.summary_label.setText("")
    
    def preview_closure(self):
        """Prévisualise la clôture"""
        try:
            selected_date = self.closure_date_edit.date().toPyDate()
            
            # Récupérer toutes les caisses actives
            from app.core.models.treasury import CashRegister
            active_registers = self.db.query(CashRegister).filter(
                CashRegister.is_active == True
            ).all()
            
            if not active_registers:
                QMessageBox.warning(self, "Attention", "Aucune caisse active trouvée")
                return
            
            # Calculer les snapshots prévisionnels
            self.preview_table.setRowCount(len(active_registers))
            
            total_opening = Decimal("0.00")
            total_closing = Decimal("0.00")
            total_in = Decimal("0.00")
            total_out = Decimal("0.00")
            total_variance = Decimal("0.00")
            
            for row, register in enumerate(active_registers):
                # Calculer le snapshot
                snapshot_data = self.closure_service.calculate_register_snapshot(
                    register.id, selected_date
                )
                
                # Remplir le tableau
                self.preview_table.setItem(row, 0, QTableWidgetItem(register.name))
                self.preview_table.setItem(row, 1, QTableWidgetItem(f"{snapshot_data['opening_balance']:.2f} DA"))
                self.preview_table.setItem(row, 2, QTableWidgetItem(f"{snapshot_data['closing_balance']:.2f} DA"))
                self.preview_table.setItem(row, 3, QTableWidgetItem(f"{snapshot_data['total_in']:.2f} DA"))
                self.preview_table.setItem(row, 4, QTableWidgetItem(f"{snapshot_data['total_out']:.2f} DA"))
                
                # Colorer l'écart selon sa valeur
                variance_item = QTableWidgetItem(f"{snapshot_data['variance']:.2f} DA")
                if abs(snapshot_data['variance']) > Decimal("1.00"):
                    variance_item.setBackground(QColor(255, 200, 200))  # Rouge clair
                elif abs(snapshot_data['variance']) > Decimal("0.01"):
                    variance_item.setBackground(QColor(255, 255, 200))  # Jaune clair
                else:
                    variance_item.setBackground(QColor(200, 255, 200))  # Vert clair
                
                self.preview_table.setItem(row, 5, variance_item)
                
                # Accumuler les totaux
                total_opening += snapshot_data['opening_balance']
                total_closing += snapshot_data['closing_balance']
                total_in += snapshot_data['total_in']
                total_out += snapshot_data['total_out']
                total_variance += snapshot_data['variance']
            
            # Afficher le résumé
            summary_text = (
                f"Résumé: {len(active_registers)} caisses | "
                f"Solde total: {total_closing:.2f} DA | "
                f"Entrées: {total_in:.2f} DA | "
                f"Sorties: {total_out:.2f} DA | "
                f"Écart total: {total_variance:.2f} DA"
            )
            
            self.summary_label.setText(summary_text)
            
            # Activer le bouton d'exécution
            self.execute_button.setEnabled(True)
            
            event_bus.show_success("Prévisualisation générée avec succès")
            
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de la prévisualisation:\n{e}")
            event_bus.show_error(f"Erreur de prévisualisation: {e}")
    
    def execute_closure(self):
        """Exécute la clôture"""
        selected_date = self.closure_date_edit.date().toPyDate()
        
        # Confirmation
        reply = QMessageBox.question(
            self, "Confirmation",
            f"Êtes-vous sûr de vouloir exécuter la clôture journalière du {selected_date}?\n\n"
            "Cette opération verrouillera définitivement la période et ne pourra pas être annulée.",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply != QMessageBox.StandardButton.Yes:
            return
        
        # Désactiver l'interface
        self.execute_button.setEnabled(False)
        self.preview_button.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_label.setVisible(True)
        
        # Lancer l'exécution en arrière-plan
        self.execution_thread = ClosureExecutionThread(
            self.closure_service, selected_date, self.current_user_id
        )
        self.execution_thread.progress_updated.connect(self.on_progress_updated)
        self.execution_thread.closure_completed.connect(self.on_closure_completed)
        self.execution_thread.error_occurred.connect(self.on_closure_error)
        self.execution_thread.start()
    
    def on_progress_updated(self, percentage: int, message: str):
        """Gère la mise à jour du progrès"""
        self.progress_bar.setValue(percentage)
        self.progress_label.setText(message)
    
    def on_closure_completed(self, closure: DailyClosure):
        """Gère la fin de clôture"""
        self.progress_bar.setVisible(False)
        self.progress_label.setVisible(False)
        
        QMessageBox.information(
            self, "Succès",
            f"Clôture journalière du {closure.closure_date} terminée avec succès!\n\n"
            f"Solde total: {closure.total_balance:.2f} DA\n"
            f"Nombre de transactions: {closure.total_transactions}\n"
            f"Durée: {closure.duration_minutes or 0} minutes"
        )
        
        # Actualiser les données
        self.load_data()
        
        # Réinitialiser l'interface
        self.on_date_changed()
    
    def on_closure_error(self, error_message: str):
        """Gère les erreurs de clôture"""
        self.progress_bar.setVisible(False)
        self.progress_label.setVisible(False)
        self.execute_button.setEnabled(True)
        self.preview_button.setEnabled(True)
        
        QMessageBox.critical(self, "Erreur", f"Erreur lors de la clôture:\n{error_message}")
    
    def load_data(self):
        """Charge les données"""
        self.load_history()
        self.load_locks()
        self.on_date_changed()
    
    def load_history(self):
        """Charge l'historique des clôtures"""
        try:
            closures = self.closure_service.get_closure_history(limit=100)
            
            self.history_table.setRowCount(len(closures))
            
            for row, closure in enumerate(closures):
                self.history_table.setItem(row, 0, QTableWidgetItem(closure.closure_date.strftime('%d/%m/%Y')))
                
                # Statut avec couleur
                status_item = QTableWidgetItem(closure.status.value)
                if closure.status == ClosureStatus.COMPLETED:
                    status_item.setBackground(QColor(200, 255, 200))
                elif closure.status == ClosureStatus.FAILED:
                    status_item.setBackground(QColor(255, 200, 200))
                elif closure.status == ClosureStatus.IN_PROGRESS:
                    status_item.setBackground(QColor(255, 255, 200))
                
                self.history_table.setItem(row, 1, status_item)
                self.history_table.setItem(row, 2, QTableWidgetItem(str(closure.total_cash_registers)))
                self.history_table.setItem(row, 3, QTableWidgetItem(f"{closure.total_balance:.2f} DA"))
                self.history_table.setItem(row, 4, QTableWidgetItem(str(closure.total_transactions)))
                self.history_table.setItem(row, 5, QTableWidgetItem(str(closure.started_by_user_id or "")))
                self.history_table.setItem(row, 6, QTableWidgetItem(
                    closure.completed_at.strftime('%d/%m/%Y %H:%M') if closure.completed_at else ""
                ))
                
                # Stocker l'ID de la clôture
                self.history_table.item(row, 0).setData(Qt.ItemDataRole.UserRole, closure.id)
            
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement de l'historique:\n{e}")
    
    def load_locks(self):
        """Charge les verrouillages"""
        try:
            locks = self.closure_service.get_period_locks()
            
            self.locks_table.setRowCount(len(locks))
            
            for row, lock in enumerate(locks):
                self.locks_table.setItem(row, 0, QTableWidgetItem(lock.start_date.strftime('%d/%m/%Y')))
                self.locks_table.setItem(row, 1, QTableWidgetItem(lock.end_date.strftime('%d/%m/%Y')))
                self.locks_table.setItem(row, 2, QTableWidgetItem(lock.lock_type))
                self.locks_table.setItem(row, 3, QTableWidgetItem(lock.reason or ""))
                self.locks_table.setItem(row, 4, QTableWidgetItem(str(lock.locked_by_user_id)))
                self.locks_table.setItem(row, 5, QTableWidgetItem(lock.locked_at.strftime('%d/%m/%Y %H:%M')))
            
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement des verrouillages:\n{e}")
    
    def on_history_selection_changed(self):
        """Gère le changement de sélection dans l'historique"""
        selected_rows = self.history_table.selectionModel().selectedRows()
        
        if selected_rows:
            self.view_details_button.setEnabled(True)
            
            # Vérifier si la clôture peut être annulée
            row = selected_rows[0].row()
            closure_id = self.history_table.item(row, 0).data(Qt.ItemDataRole.UserRole)
            
            # TODO: Récupérer la clôture et vérifier son statut
            self.cancel_closure_button.setEnabled(False)  # Pour l'instant
        else:
            self.view_details_button.setEnabled(False)
            self.cancel_closure_button.setEnabled(False)
    
    def view_closure_details(self):
        """Affiche les détails d'une clôture"""
        selected_rows = self.history_table.selectionModel().selectedRows()
        if not selected_rows:
            return
        
        row = selected_rows[0].row()
        closure_id = self.history_table.item(row, 0).data(Qt.ItemDataRole.UserRole)
        
        # TODO: Implémenter le dialogue de détails
        QMessageBox.information(self, "Détails", f"Détails de la clôture {closure_id}")
    
    def cancel_closure(self):
        """Annule une clôture"""
        # TODO: Implémenter l'annulation
        QMessageBox.information(self, "Annulation", "Fonctionnalité d'annulation à implémenter")
    
    def closeEvent(self, event):
        """Gère la fermeture du dialogue"""
        if self.execution_thread and self.execution_thread.isRunning():
            reply = QMessageBox.question(
                self, "Confirmation",
                "Une clôture est en cours. Voulez-vous vraiment fermer?",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.No:
                event.ignore()
                return
            
            self.execution_thread.terminate()
            self.execution_thread.wait()
        
        if hasattr(self, 'db') and self.db:
            self.db.close()
        
        super().closeEvent(event)
